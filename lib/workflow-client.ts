import { Client } from '@upstash/workflow';
import { logger } from './utils';

const workflowClient = new Client({
  token: process.env.QSTASH_TOKEN!,
  baseUrl: process.env.QSTASH_URL!,
});

/**
 * Trigger job processing workflow
 * Replaces the old QStash scheduled processor with atomic workflow
 */
export async function triggerJobProcessing(
  batchSize = 10,
  source?: string
): Promise<string> {
  try {
    // Use consistent base URL from environment or defaults
    const baseUrl =
      process.env.WORKFLOW_BASE_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://bordfeed.com'
        : 'http://localhost:3000');

    logger.info('🚀 Triggering job processing workflow', {
      batchSize,
      source,
      endpoint: `${baseUrl}/api/workflows/process-jobs`,
    });

    const response = await workflowClient.trigger({
      url: `${baseUrl}/api/workflows/process-jobs`,
      headers: {
        'Content-Type': 'application/json',
        // Add Vercel deployment protection bypass for production
        ...(process.env.NODE_ENV === 'production' &&
          process.env.VERCEL_PROTECTION_BYPASS && {
            'x-vercel-protection-bypass': process.env.VERCEL_PROTECTION_BYPASS,
          }),
      },
      body: JSON.stringify({ batchSize, source }),
    });

    logger.info('✅ Job processing workflow triggered', {
      workflowRunId: response.workflowRunId,
      batchSize,
      source,
    });

    return response.workflowRunId;
  } catch (error) {
    logger.error('❌ Failed to trigger job processing workflow', { error });
    throw error;
  }
}

/**
 * Trigger monitoring workflow for job status checks
 */
export async function triggerJobMonitoring(jobIds: string[]): Promise<string> {
  try {
    // Use consistent base URL from environment or defaults
    const baseUrl =
      process.env.WORKFLOW_BASE_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://bordfeed.com'
        : 'http://localhost:3000');

    logger.info('👁️ Triggering job monitoring workflow', {
      jobCount: jobIds.length,
      endpoint: `${baseUrl}/api/workflows/monitor-jobs`,
    });

    const response = await workflowClient.trigger({
      url: `${baseUrl}/api/workflows/monitor-jobs`,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ jobIds }),
    });

    logger.info('✅ Job monitoring workflow triggered', {
      workflowRunId: response.workflowRunId,
      jobCount: jobIds.length,
    });

    return response.workflowRunId;
  } catch (error) {
    logger.error('❌ Failed to trigger job monitoring workflow', { error });
    throw error;
  }
}

/**
 * Trigger job posting workflow for publishing to job boards
 */
export async function triggerJobPosting(boardId?: string): Promise<string> {
  try {
    // Use consistent base URL from environment or defaults
    const baseUrl =
      process.env.WORKFLOW_BASE_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://bordfeed.com'
        : 'http://localhost:3000');

    logger.info('📤 Triggering job posting workflow', {
      boardId,
      endpoint: `${baseUrl}/api/workflows/post-jobs`,
    });

    const response = await workflowClient.trigger({
      url: `${baseUrl}/api/workflows/post-jobs`,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ boardId }),
    });

    logger.info('✅ Job posting workflow triggered', {
      workflowRunId: response.workflowRunId,
      boardId,
    });

    return response.workflowRunId;
  } catch (error) {
    logger.error('❌ Failed to trigger job posting workflow', { error });
    throw error;
  }
}

/**
 * Trigger system health monitoring workflow
 */
export async function triggerSystemHealthMonitoring(): Promise<string> {
  try {
    // Use consistent base URL from environment or defaults
    const baseUrl =
      process.env.WORKFLOW_BASE_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://bordfeed.com'
        : 'http://localhost:3000');

    logger.info('🏥 Triggering system health monitoring workflow', {
      endpoint: `${baseUrl}/api/workflows/system-health`,
    });

    const response = await workflowClient.trigger({
      url: `${baseUrl}/api/workflows/system-health`,
      headers: {
        'Content-Type': 'application/json',
        // Add Vercel deployment protection bypass for production
        ...(process.env.NODE_ENV === 'production' &&
          process.env.VERCEL_PROTECTION_BYPASS && {
            'x-vercel-protection-bypass': process.env.VERCEL_PROTECTION_BYPASS,
          }),
      },
      body: JSON.stringify({}),
    });

    logger.info('✅ System health monitoring workflow triggered', {
      workflowRunId: response.workflowRunId,
    });

    return response.workflowRunId;
  } catch (error) {
    logger.error('❌ Failed to trigger system health monitoring workflow', {
      error,
    });
    throw error;
  }
}

/**
 * Trigger data quality monitoring workflow
 */
export async function triggerDataQualityMonitoring(options?: {
  datasetType?: 'job_processing' | 'manual_trigger';
  batchSize?: number;
  source?: string;
}): Promise<string> {
  try {
    // Use consistent base URL from environment or defaults
    const baseUrl =
      process.env.WORKFLOW_BASE_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://bordfeed.com'
        : 'http://localhost:3000');

    logger.info('📊 Triggering data quality monitoring workflow', {
      options,
      endpoint: `${baseUrl}/api/workflows/data-quality`,
    });

    const response = await workflowClient.trigger({
      url: `${baseUrl}/api/workflows/data-quality`,
      headers: {
        'Content-Type': 'application/json',
        // Add Vercel deployment protection bypass for production
        ...(process.env.NODE_ENV === 'production' &&
          process.env.VERCEL_PROTECTION_BYPASS && {
            'x-vercel-protection-bypass': process.env.VERCEL_PROTECTION_BYPASS,
          }),
      },
      body: JSON.stringify(options || {}),
    });

    logger.info('✅ Data quality monitoring workflow triggered', {
      workflowRunId: response.workflowRunId,
      options,
    });

    return response.workflowRunId;
  } catch (error) {
    logger.error('❌ Failed to trigger data quality monitoring workflow', {
      error,
    });
    throw error;
  }
}

/**
 * Trigger business logic monitoring workflow
 */
export async function triggerBusinessLogicMonitoring(options?: {
  monitoringType?: 'performance' | 'kpis' | 'predictions' | 'comprehensive';
  timeframe?: '1h' | '24h' | '7d' | '30d';
  includeForecasting?: boolean;
  triggerSource?: string;
}): Promise<string> {
  try {
    // Use consistent base URL from environment or defaults
    const baseUrl =
      process.env.WORKFLOW_BASE_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://bordfeed.com'
        : 'http://localhost:3000');

    logger.info('📈 Triggering business logic monitoring workflow', {
      options,
      endpoint: `${baseUrl}/api/workflows/business-logic`,
    });

    const response = await workflowClient.trigger({
      url: `${baseUrl}/api/workflows/business-logic`,
      headers: {
        'Content-Type': 'application/json',
        // Add Vercel deployment protection bypass for production
        ...(process.env.NODE_ENV === 'production' &&
          process.env.VERCEL_PROTECTION_BYPASS && {
            'x-vercel-protection-bypass': process.env.VERCEL_PROTECTION_BYPASS,
          }),
      },
      body: JSON.stringify(options || {}),
    });

    logger.info('✅ Business logic monitoring workflow triggered', {
      workflowRunId: response.workflowRunId,
      options,
    });

    return response.workflowRunId;
  } catch (error) {
    logger.error('❌ Failed to trigger business logic monitoring workflow', {
      error,
    });
    throw error;
  }
}

/**
 * Trigger monitoring coordinator workflow
 */
export async function triggerMonitoringCoordinator(options?: {
  schedule?: 'startup' | 'hourly' | 'daily' | 'manual';
  tiers?: ('system_health' | 'data_quality' | 'business_logic')[];
  priority?: 'low' | 'normal' | 'high' | 'critical';
  triggerSource?: string;
}): Promise<string> {
  try {
    // Use consistent base URL from environment or defaults
    const baseUrl =
      process.env.WORKFLOW_BASE_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://bordfeed.com'
        : 'http://localhost:3000');

    logger.info('🎯 Triggering monitoring coordinator workflow', {
      options,
      endpoint: `${baseUrl}/api/workflows/monitoring-coordinator`,
    });

    const response = await workflowClient.trigger({
      url: `${baseUrl}/api/workflows/monitoring-coordinator`,
      headers: {
        'Content-Type': 'application/json',
        // Add Vercel deployment protection bypass for production
        ...(process.env.NODE_ENV === 'production' &&
          process.env.VERCEL_PROTECTION_BYPASS && {
            'x-vercel-protection-bypass': process.env.VERCEL_PROTECTION_BYPASS,
          }),
      },
      body: JSON.stringify(options || {}),
    });

    logger.info('✅ Monitoring coordinator workflow triggered', {
      workflowRunId: response.workflowRunId,
      options,
    });

    return response.workflowRunId;
  } catch (error) {
    logger.error('❌ Failed to trigger monitoring coordinator workflow', {
      error,
    });
    throw error;
  }
}

/**
 * Get workflow execution status
 */
export async function getWorkflowStatus(workflowRunId: string) {
  try {
    // This would typically query the workflow status from Upstash
    // For now, we'll check our database
    const { createClient } = await import('./supabase');
    const supabase = createClient();

    const { data: run, error } = await supabase
      .from('workflow_runs')
      .select('*')
      .eq('id', workflowRunId)
      .single();

    if (error) {
      logger.error('Failed to get workflow status', { error, workflowRunId });
      return null;
    }

    return run;
  } catch (error) {
    logger.error('Error getting workflow status', { error, workflowRunId });
    return null;
  }
}

/**
 * Cancel a running workflow (if possible)
 */
export async function cancelWorkflow(workflowRunId: string): Promise<boolean> {
  try {
    // Mark as cancelled in our database
    const { createClient } = await import('./supabase');
    const supabase = createClient();

    const { error } = await supabase
      .from('workflow_runs')
      .update({
        status: 'cancelled',
        completed_at: new Date().toISOString(),
        error_message: 'Cancelled by user',
      })
      .eq('id', workflowRunId)
      .eq('status', 'running');

    if (error) {
      logger.error('Failed to cancel workflow', { error, workflowRunId });
      return false;
    }

    logger.info('✅ Workflow cancelled', { workflowRunId });
    return true;
  } catch (error) {
    logger.error('Error cancelling workflow', { error, workflowRunId });
    return false;
  }
}
