import {
  <PERSON><PERSON><PERSON>,
  Minus,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  TrendingDown,
  TrendingUp,
  <PERSON>,
  Zap,
} from 'lucide-react';
import type { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { createServerClient } from '@/lib/supabase';
import { triggerBusinessLogicMonitoring } from '@/lib/workflow-client';

export const metadata: Metadata = {
  title: 'Business Intelligence | Bordfeed Dashboard',
  description: 'AI-powered business logic monitoring and strategic insights',
};

interface BusinessLogicLog {
  id: string;
  timestamp: string;
  monitoring_type: string;
  timeframe: string;
  business_health: string;
  confidence: number;
  business_value_score: number;
  efficiency_score: number;
  scalability_index: number;
  reliability_score: number;
  innovation_index: number;
  predicted_job_volume: number;
  cost_optimization_potential: number;
  insights_count: number;
  recommendations_count: number;
  alert_level: string;
  actions_recommended: string[];
  executive_summary: string;
}

interface BusinessTrend {
  hour: string;
  monitoring_type: string;
  business_health: string;
  check_count: number;
  avg_value_score: number;
  avg_efficiency: number;
  avg_scalability: number;
  avg_reliability: number;
  avg_innovation: number;
  avg_predicted_volume: number;
  avg_cost_optimization: number;
  total_insights: number;
  total_recommendations: number;
}

async function getBusinessLogicLogs(): Promise<BusinessLogicLog[]> {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from('business_logic_logs')
    .select('*')
    .order('timestamp', { ascending: false })
    .limit(50);

  if (error) {
    console.error('Error fetching business logic logs:', error);
    return [];
  }

  return data || [];
}

async function getBusinessTrends(): Promise<BusinessTrend[]> {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from('business_performance_trends')
    .select('*')
    .order('hour', { ascending: false })
    .limit(24);

  if (error) {
    console.error('Error fetching business trends:', error);
    return [];
  }

  return data || [];
}

function getBusinessHealthColor(health: string): string {
  switch (health) {
    case 'EXCELLENT':
      return 'bg-green-500';
    case 'GOOD':
      return 'bg-blue-500';
    case 'FAIR':
      return 'bg-yellow-500';
    case 'CONCERNING':
      return 'bg-orange-500';
    case 'CRITICAL':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
}

function getBusinessHealthBadgeVariant(
  health: string
): 'default' | 'destructive' | 'secondary' | 'outline' {
  switch (health) {
    case 'EXCELLENT':
    case 'GOOD':
      return 'default';
    case 'FAIR':
      return 'secondary';
    case 'CONCERNING':
    case 'CRITICAL':
      return 'destructive';
    default:
      return 'outline';
  }
}

function getPerformanceIcon(score: number, previousScore?: number) {
  if (!previousScore)
    return <Minus className="h-4 w-4 text-muted-foreground" />;

  if (score > previousScore) {
    return <TrendingUp className="h-4 w-4 text-green-500" />;
  }
  if (score < previousScore) {
    return <TrendingDown className="h-4 w-4 text-red-500" />;
  }
  return <Minus className="h-4 w-4 text-muted-foreground" />;
}

function getScoreColor(score: number): string {
  if (score >= 90) return 'text-green-500';
  if (score >= 75) return 'text-blue-500';
  if (score >= 60) return 'text-yellow-500';
  if (score >= 40) return 'text-orange-500';
  return 'text-red-500';
}

async function triggerBusinessAnalysis() {
  'use server';

  try {
    await triggerBusinessLogicMonitoring({
      monitoringType: 'comprehensive',
      timeframe: '24h',
      includeForecasting: true,
      triggerSource: 'manual_dashboard',
    });
  } catch (error) {
    console.error('Failed to trigger business analysis:', error);
  }

  redirect('/dashboard/business-logic');
}

export default async function BusinessLogicPage() {
  const [businessLogs, businessTrends] = await Promise.all([
    getBusinessLogicLogs(),
    getBusinessTrends(),
  ]);

  const latestBusiness = businessLogs[0];
  const previousBusiness = businessLogs[1];

  return (
    <div className="container mx-auto space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-3xl">Business Intelligence</h1>
          <p className="text-muted-foreground">
            AI-powered strategic insights and performance analytics
          </p>
        </div>

        <form action={triggerBusinessAnalysis}>
          <Button size="sm" type="submit" variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Run Analysis
          </Button>
        </form>
      </div>

      {/* Executive Summary */}
      {latestBusiness && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div
                className={`h-3 w-3 rounded-full ${getBusinessHealthColor(latestBusiness.business_health)}`}
              />
              Executive Summary
              <Badge
                variant={getBusinessHealthBadgeVariant(
                  latestBusiness.business_health
                )}
              >
                {latestBusiness.business_health}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed">
              {latestBusiness.executive_summary}
            </p>
            <div className="mt-4 flex items-center gap-4 text-muted-foreground text-xs">
              <span>
                Confidence: {(latestBusiness.confidence * 100).toFixed(0)}%
              </span>
              <span>Insights: {latestBusiness.insights_count}</span>
              <span>
                Recommendations: {latestBusiness.recommendations_count}
              </span>
              <span>Timeframe: {latestBusiness.timeframe}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Metrics */}
      {latestBusiness && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="font-medium text-sm">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Efficiency
                </div>
              </CardTitle>
              {getPerformanceIcon(
                latestBusiness.efficiency_score,
                previousBusiness?.efficiency_score
              )}
            </CardHeader>
            <CardContent>
              <div
                className={`font-bold text-2xl ${getScoreColor(latestBusiness.efficiency_score)}`}
              >
                {latestBusiness.efficiency_score}%
              </div>
              <p className="text-muted-foreground text-xs">
                Processing & AI performance
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="font-medium text-sm">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Scalability
                </div>
              </CardTitle>
              {getPerformanceIcon(
                latestBusiness.scalability_index,
                previousBusiness?.scalability_index
              )}
            </CardHeader>
            <CardContent>
              <div
                className={`font-bold text-2xl ${getScoreColor(latestBusiness.scalability_index)}`}
              >
                {latestBusiness.scalability_index}%
              </div>
              <p className="text-muted-foreground text-xs">
                System growth capacity
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="font-medium text-sm">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Reliability
                </div>
              </CardTitle>
              {getPerformanceIcon(
                latestBusiness.reliability_score,
                previousBusiness?.reliability_score
              )}
            </CardHeader>
            <CardContent>
              <div
                className={`font-bold text-2xl ${getScoreColor(latestBusiness.reliability_score)}`}
              >
                {latestBusiness.reliability_score}%
              </div>
              <p className="text-muted-foreground text-xs">
                Uptime & error rates
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="font-medium text-sm">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Innovation
                </div>
              </CardTitle>
              {getPerformanceIcon(
                latestBusiness.innovation_index,
                previousBusiness?.innovation_index
              )}
            </CardHeader>
            <CardContent>
              <div
                className={`font-bold text-2xl ${getScoreColor(latestBusiness.innovation_index)}`}
              >
                {latestBusiness.innovation_index}%
              </div>
              <p className="text-muted-foreground text-xs">
                AI & technology adoption
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Business Value & Predictions */}
      {latestBusiness && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Business Value & Optimization
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Business Value Score</span>
                <div
                  className={`font-bold text-lg ${getScoreColor(latestBusiness.business_value_score)}`}
                >
                  {latestBusiness.business_value_score}/100
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Cost Optimization Potential</span>
                <div className="font-bold text-green-500 text-lg">
                  {latestBusiness.cost_optimization_potential}%
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Predicted Job Volume (24h)</span>
                <div className="font-bold text-blue-500 text-lg">
                  {latestBusiness.predicted_job_volume.toLocaleString()}
                </div>
              </div>

              {latestBusiness.alert_level !== 'none' && (
                <div className="rounded bg-muted p-3">
                  <Badge
                    className="mb-2"
                    variant={
                      latestBusiness.alert_level === 'critical'
                        ? 'destructive'
                        : 'secondary'
                    }
                  >
                    {latestBusiness.alert_level.toUpperCase()} ALERT
                  </Badge>
                  {latestBusiness.actions_recommended.length > 0 && (
                    <div>
                      <div className="mb-1 font-medium text-xs">
                        Recommended Actions:
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {latestBusiness.actions_recommended.map(
                          (action, index) => (
                            <Badge
                              className="text-xs"
                              key={index}
                              variant="outline"
                            >
                              {action.replace(/-/g, ' ')}
                            </Badge>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Business Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              {businessLogs.length === 0 ? (
                <div className="py-8 text-center text-muted-foreground">
                  No business analysis available. Run an analysis to get
                  started.
                </div>
              ) : (
                <div className="space-y-3">
                  {businessLogs.slice(0, 6).map((log) => (
                    <div
                      className="flex items-center justify-between rounded border p-3"
                      key={log.id}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`h-3 w-3 rounded-full ${getBusinessHealthColor(log.business_health)}`}
                        />
                        <div>
                          <div className="font-medium text-sm">
                            {new Date(log.timestamp).toLocaleString()}
                          </div>
                          <div className="text-muted-foreground text-xs">
                            {log.monitoring_type} | {log.timeframe}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 text-right">
                        <div className="text-sm">
                          <div
                            className={`font-bold ${getScoreColor(log.business_value_score)}`}
                          >
                            {log.business_value_score}/100
                          </div>
                          <div className="text-muted-foreground text-xs">
                            {log.cost_optimization_potential}% savings
                          </div>
                        </div>
                        <Badge
                          variant={getBusinessHealthBadgeVariant(
                            log.business_health
                          )}
                        >
                          {log.business_health}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Performance Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends (24h)</CardTitle>
        </CardHeader>
        <CardContent>
          {businessTrends.length === 0 ? (
            <div className="py-8 text-center text-muted-foreground">
              No trend data available.
            </div>
          ) : (
            <div className="space-y-3">
              {businessTrends.slice(0, 12).map((trend) => (
                <div
                  className="flex items-center justify-between rounded p-3 hover:bg-muted/50"
                  key={`${trend.hour}-${trend.monitoring_type}`}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`h-2 w-2 rounded-full ${getBusinessHealthColor(trend.business_health)}`}
                    />
                    <div>
                      <span className="font-medium text-sm">
                        {new Date(trend.hour).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit',
                        })}
                      </span>
                      <div className="text-muted-foreground text-xs">
                        {trend.check_count} checks |{' '}
                        {Math.round(trend.avg_predicted_volume)} jobs predicted
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-center text-xs">
                    <div className={getScoreColor(trend.avg_efficiency)}>
                      E: {Math.round(trend.avg_efficiency)}%
                    </div>
                    <div className={getScoreColor(trend.avg_scalability)}>
                      S: {Math.round(trend.avg_scalability)}%
                    </div>
                    <div className={getScoreColor(trend.avg_reliability)}>
                      R: {Math.round(trend.avg_reliability)}%
                    </div>
                    <div className={getScoreColor(trend.avg_innovation)}>
                      I: {Math.round(trend.avg_innovation)}%
                    </div>
                  </div>

                  <div className="text-right">
                    <div
                      className={`font-bold text-sm ${getScoreColor(trend.avg_value_score)}`}
                    >
                      {Math.round(trend.avg_value_score)}/100
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {Math.round(trend.avg_cost_optimization)}% savings
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {latestBusiness && (
        <div className="text-muted-foreground text-xs">
          Last analysis: {new Date(latestBusiness.timestamp).toLocaleString()} |
          Confidence: {(latestBusiness.confidence * 100).toFixed(0)}%
        </div>
      )}
    </div>
  );
}
