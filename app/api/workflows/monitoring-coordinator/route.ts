import { serve } from '@upstash/workflow/nextjs';
import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';
import {
  triggerBusinessLogicMonitoring,
  triggerDataQualityMonitoring,
  triggerSystemHealthMonitoring,
} from '@/lib/workflow-client';

// Monitoring Coordinator Payload
interface MonitoringCoordinatorPayload {
  schedule?: 'startup' | 'hourly' | 'daily' | 'manual';
  tiers?: ('system_health' | 'data_quality' | 'business_logic')[];
  priority?: 'low' | 'normal' | 'high' | 'critical';
  triggerSource?: string;
}

// Monitoring execution results
interface MonitoringExecution {
  tier: string;
  workflowRunId: string;
  success: boolean;
  startTime: string;
  duration?: number;
  error?: string;
}

// Comprehensive monitoring schedule configurations
const MONITORING_SCHEDULES = {
  startup: {
    description: 'Initial system startup monitoring',
    tiers: ['system_health', 'data_quality', 'business_logic'],
    parallel: false, // Sequential execution for startup
    intervals: {
      system_health: 0,
      data_quality: 30_000,
      business_logic: 60_000,
    }, // 30s, 1m delays
  },
  hourly: {
    description: 'Hourly comprehensive monitoring',
    tiers: ['system_health', 'data_quality'],
    parallel: true,
    intervals: { system_health: 0, data_quality: 5000 }, // 5s delay
  },
  daily: {
    description: 'Daily business intelligence and comprehensive analysis',
    tiers: ['system_health', 'data_quality', 'business_logic'],
    parallel: false, // Sequential for thorough analysis
    intervals: {
      system_health: 0,
      data_quality: 60_000,
      business_logic: 120_000,
    }, // 1m, 2m delays
  },
  manual: {
    description: 'Manual triggered monitoring',
    tiers: ['system_health', 'data_quality', 'business_logic'],
    parallel: true,
    intervals: { system_health: 0, data_quality: 0, business_logic: 0 }, // No delays
  },
} as const;

async function executeMonitoringTier(
  tier: string,
  schedule: string
): Promise<MonitoringExecution> {
  const startTime = new Date().toISOString();
  const executionStart = Date.now();

  try {
    let workflowRunId: string;

    switch (tier) {
      case 'system_health':
        workflowRunId = await triggerSystemHealthMonitoring();
        break;

      case 'data_quality':
        workflowRunId = await triggerDataQualityMonitoring({
          datasetType: 'job_processing',
          source: `coordinator-${schedule}`,
        });
        break;

      case 'business_logic':
        workflowRunId = await triggerBusinessLogicMonitoring({
          monitoringType: 'comprehensive',
          timeframe: schedule === 'daily' ? '24h' : '1h',
          includeForecasting: schedule === 'daily',
          triggerSource: `coordinator-${schedule}`,
        });
        break;

      default:
        throw new Error(`Unknown monitoring tier: ${tier}`);
    }

    const duration = Date.now() - executionStart;

    logger.info('✅ Monitoring tier executed successfully', {
      tier,
      schedule,
      workflowRunId,
      duration,
    });

    return {
      tier,
      workflowRunId,
      success: true,
      startTime,
      duration,
    };
  } catch (error) {
    const duration = Date.now() - executionStart;

    logger.error('❌ Monitoring tier execution failed', {
      tier,
      schedule,
      error,
      duration,
    });

    return {
      tier,
      workflowRunId: '',
      success: false,
      startTime,
      duration,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function sendCoordinatorAlert(
  title: string,
  data: any,
  priority: 'info' | 'warning' | 'critical' = 'info'
): Promise<void> {
  const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
  if (!slackWebhookUrl) return;

  const emoji = {
    info: '🔄',
    warning: '⚠️',
    critical: '🚨',
  }[priority];

  try {
    await fetch(slackWebhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text: `${emoji} ${title}`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*${emoji} ${title}*\n\`\`\`\n${JSON.stringify(data, null, 2)}\n\`\`\``,
            },
          },
        ],
      }),
    });
  } catch (error) {
    logger.error('Failed to send coordinator Slack alert', { title, error });
  }
}

async function scheduleNextCoordinatorRun(
  schedule: string,
  context: any
): Promise<void> {
  const scheduleIntervals = {
    hourly: '1h',
    daily: '24h',
  } as const;

  const interval =
    scheduleIntervals[schedule as keyof typeof scheduleIntervals];
  if (!interval) return; // No recurring schedule for startup/manual

  try {
    // Schedule next monitoring run
    await context.sleep(`wait-${interval}`, interval);

    // Trigger next coordinator run
    await context.run('trigger-next-coordinator-run', async () => {
      const response = await fetch(
        `${process.env.WORKFLOW_BASE_URL || 'https://bordfeed.com'}/api/workflows/monitoring-coordinator`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            schedule,
            priority: 'normal',
            triggerSource: `auto-${schedule}`,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to schedule next run: ${response.status}`);
      }

      logger.info(`📅 Next ${schedule} monitoring scheduled`, {
        interval,
        nextRun: new Date(
          Date.now() + (interval === '1h' ? 3_600_000 : 86_400_000)
        ).toISOString(),
      });
    });
  } catch (error) {
    logger.error(`Failed to schedule next ${schedule} monitoring`, { error });
  }
}

// Main Monitoring Coordinator Workflow
export const { POST } = serve<MonitoringCoordinatorPayload>(
  async (context) => {
    const payload = context.requestPayload || {};
    const schedule = payload.schedule || 'manual';
    const customTiers = payload.tiers;
    const priority = payload.priority || 'normal';
    const triggerSource = payload.triggerSource || 'unknown';

    logger.info('🎯 Starting monitoring coordinator workflow', {
      schedule,
      customTiers,
      priority,
      triggerSource,
    });

    // Get monitoring configuration
    const config =
      MONITORING_SCHEDULES[schedule as keyof typeof MONITORING_SCHEDULES];
    const tiersToExecute = customTiers || config.tiers;

    // Step 1: Execute monitoring tiers
    const executions: MonitoringExecution[] = [];

    if (config.parallel) {
      // Parallel execution for faster monitoring
      const parallelExecutions = await context.run(
        'execute-parallel-monitoring',
        async () => {
          logger.info('Executing monitoring tiers in parallel', {
            tiers: tiersToExecute,
          });

          const promises = tiersToExecute.map(async (tier, index) => {
            // Add small delays even in parallel mode to avoid overwhelming systems
            if (config.intervals[tier as keyof typeof config.intervals] > 0) {
              await new Promise((resolve) =>
                setTimeout(
                  resolve,
                  config.intervals[tier as keyof typeof config.intervals]
                )
              );
            }
            return executeMonitoringTier(tier, schedule);
          });

          return await Promise.all(promises);
        }
      );

      executions.push(...parallelExecutions);
    } else {
      // Sequential execution for thorough analysis
      for (const tier of tiersToExecute) {
        const execution = await context.run(
          `execute-${tier}-monitoring`,
          async () => {
            logger.info(`Executing ${tier} monitoring (sequential)`, {
              tier,
              schedule,
            });

            // Add configured delay before execution
            const delay =
              config.intervals[tier as keyof typeof config.intervals];
            if (delay > 0) {
              logger.info(`Waiting ${delay}ms before ${tier} execution`);
              await new Promise((resolve) => setTimeout(resolve, delay));
            }

            return executeMonitoringTier(tier, schedule);
          }
        );

        executions.push(execution);
      }
    }

    // Step 2: Analyze Execution Results
    const analysisResults = await context.run(
      'analyze-execution-results',
      async () => {
        const successfulExecutions = executions.filter((e) => e.success);
        const failedExecutions = executions.filter((e) => !e.success);
        const totalDuration = executions.reduce(
          (sum, e) => sum + (e.duration || 0),
          0
        );
        const avgDuration =
          executions.length > 0 ? totalDuration / executions.length : 0;

        const analysis = {
          totalTiers: executions.length,
          successfulTiers: successfulExecutions.length,
          failedTiers: failedExecutions.length,
          successRate:
            executions.length > 0
              ? (successfulExecutions.length / executions.length) * 100
              : 0,
          totalDuration,
          avgDuration: Math.round(avgDuration),
          executions: executions.map((e) => ({
            tier: e.tier,
            success: e.success,
            duration: e.duration,
            workflowRunId: e.workflowRunId,
          })),
          failureDetails: failedExecutions.map((e) => ({
            tier: e.tier,
            error: e.error,
          })),
        };

        logger.info('📊 Monitoring execution analysis completed', analysis);
        return analysis;
      }
    );

    // Step 3: Send Alerts for Failed Executions
    if (analysisResults.failedTiers > 0) {
      await context.run('send-failure-alerts', async () => {
        const failurePriority =
          analysisResults.failedTiers >= 2 ? 'critical' : 'warning';

        await sendCoordinatorAlert(
          `MONITORING COORDINATOR: ${analysisResults.failedTiers}/${analysisResults.totalTiers} Tiers Failed`,
          {
            schedule,
            successRate: `${analysisResults.successRate.toFixed(1)}%`,
            failed: analysisResults.failedTiers,
            failures: analysisResults.failureDetails,
            totalDuration: `${analysisResults.totalDuration}ms`,
            triggerSource,
            timestamp: new Date().toISOString(),
          },
          failurePriority
        );
      });
    }

    // Step 4: Send Success Summary (for daily/critical schedules)
    if (
      schedule === 'daily' ||
      priority === 'critical' ||
      analysisResults.successRate === 100
    ) {
      await context.run('send-success-summary', async () => {
        await sendCoordinatorAlert(
          `MONITORING COORDINATOR: ${schedule.toUpperCase()} Execution Complete`,
          {
            schedule,
            successRate: `${analysisResults.successRate.toFixed(1)}%`,
            tiersExecuted: analysisResults.totalTiers,
            successful: analysisResults.successfulTiers,
            totalDuration: `${analysisResults.totalDuration}ms`,
            avgDurationPerTier: `${analysisResults.avgDuration}ms`,
            executions: analysisResults.executions,
            triggerSource,
            timestamp: new Date().toISOString(),
          },
          'info'
        );
      });
    }

    // Step 5: Schedule Next Run (for recurring schedules)
    if (schedule === 'hourly' || schedule === 'daily') {
      await scheduleNextCoordinatorRun(schedule, context);
    }

    // Step 6: Log Final Summary
    await context.run('log-final-summary', async () => {
      logger.info('🎯 Monitoring coordinator workflow completed', {
        schedule,
        successRate: analysisResults.successRate,
        totalTiers: analysisResults.totalTiers,
        successful: analysisResults.successfulTiers,
        failed: analysisResults.failedTiers,
        totalDuration: analysisResults.totalDuration,
        nextSchedule:
          schedule === 'hourly'
            ? 'in 1 hour'
            : schedule === 'daily'
              ? 'in 24 hours'
              : 'manual only',
      });
    });

    return {
      success: analysisResults.successRate > 0,
      schedule,
      successRate: analysisResults.successRate,
      totalTiers: analysisResults.totalTiers,
      successfulTiers: analysisResults.successfulTiers,
      failedTiers: analysisResults.failedTiers,
      totalDuration: analysisResults.totalDuration,
      executions: analysisResults.executions,
      nextScheduledRun:
        schedule === 'hourly' ? '1h' : schedule === 'daily' ? '24h' : null,
    };
  },
  {
    retries: 1, // Light retries for coordinator - individual tiers handle their own retries
    failureFunction: async ({ context, failStatus }) => {
      logger.error('🚨 Monitoring coordinator workflow failed', {
        failStatus,
        payload: context.requestPayload,
        timestamp: new Date().toISOString(),
      });

      await sendCoordinatorAlert(
        '💥 MONITORING COORDINATOR CRITICAL FAILURE',
        {
          message: 'Master monitoring coordinator has failed',
          failStatus,
          payload: context.requestPayload,
          timestamp: new Date().toISOString(),
          action:
            'Manual intervention required - monitoring system compromised',
          impact: 'All automated monitoring temporarily disabled',
        },
        'critical'
      );
    },
  }
);

// Manual trigger endpoint
export async function GET() {
  try {
    const response = await fetch(
      `${process.env.WORKFLOW_BASE_URL || 'https://bordfeed.com'}/api/workflows/monitoring-coordinator`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          schedule: 'manual',
          priority: 'high',
          triggerSource: 'api_endpoint',
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Coordinator trigger failed: ${response.status}`);
    }

    const result = await response.json();

    return NextResponse.json({
      success: true,
      message: 'Monitoring coordinator triggered',
      workflowRunId: result.workflowRunId,
    });
  } catch (error) {
    logger.error('Failed to trigger monitoring coordinator', { error });

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to trigger monitoring coordinator',
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}
