import { serve } from '@upstash/workflow/nextjs';
import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';
import { createServerClient } from '@/lib/supabase';

// Health check functions - using existing ones from lib/health-check-utils.ts
interface HealthCheck {
  status: 'healthy' | 'degraded' | 'error';
  latency: number;
  error?: string;
}

interface SystemHealthReport {
  timestamp: string;
  checks: {
    database: HealthCheck;
    workflow: HealthCheck;
    apify: HealthCheck;
    slack: HealthCheck;
  };
  overallStatus: 'healthy' | 'degraded' | 'unhealthy';
  actions: string[];
}

async function checkDatabase(): Promise<HealthCheck> {
  try {
    const startTime = Date.now();
    const supabase = await createServerClient();

    // Simple health check query
    const { error } = await supabase.from('jobs').select('id').limit(1);

    if (error) throw error;

    return {
      status: 'healthy',
      latency: Date.now() - startTime,
    };
  } catch (error) {
    return {
      status: 'error',
      latency: 0,
      error: (error as Error).message,
    };
  }
}

async function checkWorkflow(): Promise<HealthCheck> {
  try {
    const startTime = Date.now();

    // Check if we can reach QStash API
    const response = await fetch(
      'https://qstash.upstash.io/v2/workflows/runs',
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${process.env.QSTASH_TOKEN}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`QStash API error: ${response.status}`);
    }

    return {
      status: 'healthy',
      latency: Date.now() - startTime,
    };
  } catch (error) {
    return {
      status: 'error',
      latency: 0,
      error: (error as Error).message,
    };
  }
}

async function checkApify(): Promise<HealthCheck> {
  try {
    const startTime = Date.now();

    // Check Apify API health
    const response = await fetch('https://api.apify.com/v2/users/me', {
      headers: {
        Authorization: `Bearer ${process.env.APIFY_TOKEN}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Apify API error: ${response.status}`);
    }

    return {
      status: 'healthy',
      latency: Date.now() - startTime,
    };
  } catch (error) {
    return {
      status: 'error',
      latency: 0,
      error: (error as Error).message,
    };
  }
}

async function checkSlack(): Promise<HealthCheck> {
  try {
    const startTime = Date.now();
    const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;

    if (!slackWebhookUrl) {
      throw new Error('Slack webhook URL not configured');
    }

    // Validate URL format without sending test message
    const url = new URL(slackWebhookUrl);
    if (!url.hostname.includes('hooks.slack.com')) {
      throw new Error('Invalid Slack webhook URL format');
    }

    return {
      status: 'healthy',
      latency: Date.now() - startTime,
    };
  } catch (error) {
    return {
      status: 'error',
      latency: 0,
      error: (error as Error).message,
    };
  }
}

async function sendSlackAlert(title: string, data: any): Promise<void> {
  const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
  if (!slackWebhookUrl) return;

  try {
    await fetch(slackWebhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text: title,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*${title}*\n\`\`\`\n${JSON.stringify(data, null, 2)}\n\`\`\``,
            },
          },
        ],
      }),
    });
  } catch (error) {
    logger.error('Failed to send Slack alert', { title, error });
  }
}

async function storeHealthMetrics(report: SystemHealthReport): Promise<void> {
  try {
    const supabase = await createServerClient();

    // Store in a monitoring table (you might need to create this)
    await supabase.from('system_health_logs').insert({
      timestamp: report.timestamp,
      overall_status: report.overallStatus,
      database_status: report.checks.database.status,
      database_latency: report.checks.database.latency,
      workflow_status: report.checks.workflow.status,
      workflow_latency: report.checks.workflow.latency,
      apify_status: report.checks.apify.status,
      apify_latency: report.checks.apify.latency,
      slack_status: report.checks.slack.status,
      slack_latency: report.checks.slack.latency,
      actions: report.actions,
      raw_data: report,
    });

    logger.info('✅ Health metrics stored', {
      overallStatus: report.overallStatus,
    });
  } catch (error) {
    logger.error('❌ Failed to store health metrics', { error });
  }
}

function determineOverallStatus(
  checks: SystemHealthReport['checks']
): 'healthy' | 'degraded' | 'unhealthy' {
  const statuses = Object.values(checks).map((check) => check.status);
  const errorCount = statuses.filter((status) => status === 'error').length;
  const degradedCount = statuses.filter(
    (status) => status === 'degraded'
  ).length;

  if (errorCount === 0 && degradedCount === 0) {
    return 'healthy';
  }

  if (errorCount <= 1) {
    return 'degraded';
  }

  return 'unhealthy';
}

async function triggerSystemHealthMonitoring(): Promise<string> {
  try {
    const response = await fetch(
      `${process.env.WORKFLOW_BASE_URL || 'https://bordfeed.com'}/api/workflows/system-health`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to trigger health monitoring: ${response.status}`
      );
    }

    const result = await response.json();
    return result.workflowRunId || 'triggered';
  } catch (error) {
    logger.error('Failed to trigger health monitoring', { error });
    throw error;
  }
}

// Main Workflow
export const { POST } = serve(
  async (context) => {
    logger.info('🏥 Starting system health monitoring workflow');

    // Step 1: Run all health checks in parallel
    const healthResults = await context.run('health-checks', async () => {
      logger.info('Running parallel health checks...');

      const [database, workflow, apify, slack] = await Promise.all([
        checkDatabase(),
        checkWorkflow(),
        checkApify(),
        checkSlack(),
      ]);

      return { database, workflow, apify, slack };
    });

    // Step 2: Determine overall system health
    const overallStatus = determineOverallStatus(healthResults);
    const actions: string[] = [];

    // Step 3: Handle critical issues
    if (overallStatus === 'unhealthy') {
      await context.run('handle-critical-issues', async () => {
        logger.error('🚨 CRITICAL: System unhealthy', { healthResults });

        actions.push('critical-alert-sent');
        await sendSlackAlert('🚨 SYSTEM CRITICAL - Multiple Services Down', {
          overallStatus,
          failedServices: Object.entries(healthResults)
            .filter(([_, check]) => check.status === 'error')
            .map(([service, _]) => service),
          timestamp: new Date().toISOString(),
        });
      });
    } else if (overallStatus === 'degraded') {
      await context.run('handle-degraded-performance', async () => {
        logger.warn('⚠️ System degraded', { healthResults });

        actions.push('degraded-alert-sent');
        await sendSlackAlert('⚠️ System Performance Degraded', {
          overallStatus,
          issues: Object.entries(healthResults)
            .filter(([_, check]) => check.status !== 'healthy')
            .map(
              ([service, check]) => `${service}: ${check.error || 'degraded'}`
            ),
          timestamp: new Date().toISOString(),
        });
      });
    }

    // Step 4: Store health metrics
    const healthReport: SystemHealthReport = {
      timestamp: new Date().toISOString(),
      checks: healthResults,
      overallStatus,
      actions,
    };

    await context.run('store-health-metrics', async () => {
      await storeHealthMetrics(healthReport);
    });

    // Step 5: Log summary
    await context.run('log-summary', async () => {
      logger.info('🏥 Health check completed', {
        overallStatus,
        latencies: {
          database: healthResults.database.latency,
          workflow: healthResults.workflow.latency,
          apify: healthResults.apify.latency,
          slack: healthResults.slack.latency,
        },
        actions,
      });
    });

    // Step 6: Wait 5 minutes before next check
    await context.sleep('wait-5-minutes', '5m');

    // Step 7: Schedule next health check (self-perpetuating)
    await context.run('trigger-next-check', async () => {
      logger.info('🔄 Scheduling next health check in 5 minutes');
      await triggerSystemHealthMonitoring();
    });

    return {
      success: true,
      overallStatus,
      nextCheckIn: '5 minutes',
      actions,
    };
  },
  {
    retries: 1, // Light retries for health checks
    failureFunction: async ({ context, failStatus, failResponse }) => {
      logger.error('🚨 System health monitoring workflow failed', {
        failStatus,
        timestamp: new Date().toISOString(),
      });

      // Emergency alert - the monitoring system itself is down
      await sendSlackAlert('💥 MONITORING SYSTEM FAILURE', {
        message: 'Health monitoring workflow has failed',
        failStatus,
        timestamp: new Date().toISOString(),
        action: 'Manual intervention required',
      });
    },
  }
);

// Manual trigger endpoint
export async function GET() {
  try {
    const workflowRunId = await triggerSystemHealthMonitoring();

    return NextResponse.json({
      success: true,
      message: 'System health monitoring triggered',
      workflowRunId,
    });
  } catch (error) {
    logger.error('Failed to trigger health monitoring', { error });

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to trigger health monitoring',
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}
